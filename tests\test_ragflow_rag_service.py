import unittest
from unittest.mock import AsyncMock, patch, Mock
import httpx
from core.rag.ragflow import <PERSON>gFlowRAGService, Rag<PERSON>lowAuthenticationError, Rag<PERSON>lowNetworkError, Rag<PERSON>lowAPIError
from core.rag.rag_service import FileUploadResult
from core.services.database.schemas.user_uploaded_files import FileStatus
from core.rag.ragflow import RagFlowConfig


class TestRagFlowRAGService(unittest.IsolatedAsyncioTestCase):
    """测试RagFlowRAGService类的所有方法"""

    def setUp(self):
        """测试前的设置"""
        self.ragflow_config = RagFlowConfig(
            base_url="http://localhost:9380",
            api_key="test-api-key",
            dataset_id="test-dataset-id",
            timeout=30,
            chunk_method="naive",
        )
        self.service = RagFlowRAGService(ragflow_config=self.ragflow_config)
        
        # 测试数据
        self.test_file_content = b"This is test file content"
        self.test_file_name = "test.txt"
        self.test_file_size = len(self.test_file_content)
        self.test_file_hash = "test-hash-123"
        self.test_file_type = "text/plain"
        self.test_file_id = "test-doc-id-123"
        self.test_user_id = "test-user-123"

    def test_init_with_config(self):
        """测试使用配置初始化"""
        service = RagFlowRAGService(ragflow_config=self.ragflow_config)
        self.assertEqual(service.ragflow_config.base_url, "http://localhost:9380")
        self.assertEqual(service.ragflow_config.api_key, "test-api-key")
        self.assertEqual(service.ragflow_config.dataset_id, "test-dataset-id")

    def test_init_without_config_raises_error(self):
        """测试没有配置时抛出错误"""
        with patch('core.config.app_config.config') as mock_config:
            mock_config.ragflow = None
            with self.assertRaises(ValueError):
                RagFlowRAGService()

    def test_get_api_url(self):
        """测试API URL构建"""
        url = self.service._get_api_url("datasets/123/documents")
        expected = "http://localhost:9380/api/v1/datasets/123/documents"
        self.assertEqual(url, expected)

    def test_map_ragflow_status_to_file_status(self):
        """测试状态映射"""
        test_cases = [
            ("0", FileStatus.UPLOADING),
            ("1", FileStatus.READY),
            ("2", FileStatus.PARSING),
            ("3", FileStatus.ERROR),
            ("uploading", FileStatus.UPLOADING),
            ("parsing", FileStatus.PARSING),
            ("ready", FileStatus.READY),
            ("completed", FileStatus.READY),
            ("error", FileStatus.ERROR),
            ("failed", FileStatus.ERROR),
            ("unknown", FileStatus.ERROR),
        ]
        
        for ragflow_status, expected_status in test_cases:
            with self.subTest(ragflow_status=ragflow_status):
                result = self.service._map_ragflow_status_to_file_status(ragflow_status)
                self.assertEqual(result, expected_status)

    def test_handle_http_error_401(self):
        """测试401认证错误处理"""
        mock_response = Mock()
        mock_response.status_code = 401
        
        with self.assertRaises(RagFlowAuthenticationError):
            self.service._handle_http_error(mock_response, "test operation")

    def test_handle_http_error_403(self):
        """测试403权限错误处理"""
        mock_response = Mock()
        mock_response.status_code = 403
        
        with self.assertRaises(RagFlowAuthenticationError):
            self.service._handle_http_error(mock_response, "test operation")

    def test_handle_http_error_404(self):
        """测试404资源未找到错误处理"""
        mock_response = Mock()
        mock_response.status_code = 404
        
        with self.assertRaises(RagFlowAPIError):
            self.service._handle_http_error(mock_response, "test operation")

    def test_handle_http_error_500(self):
        """测试500服务器错误处理"""
        mock_response = Mock()
        mock_response.status_code = 500
        
        with self.assertRaises(RagFlowAPIError):
            self.service._handle_http_error(mock_response, "test operation")

    @patch('httpx.AsyncClient')
    async def test_make_async_request_success(self, mock_client_class):
        """测试异步请求成功"""
        mock_client = AsyncMock()
        mock_response = Mock()
        mock_response.status_code = 200
        mock_client.request.return_value = mock_response
        mock_client_class.return_value.__aenter__.return_value = mock_client
        
        result = await self.service._make_async_request("GET", "http://test.com")
        self.assertEqual(result, mock_response)

    @patch('httpx.AsyncClient')
    async def test_make_async_request_timeout(self, mock_client_class):
        """测试异步请求超时"""
        mock_client = AsyncMock()
        mock_client.request.side_effect = httpx.TimeoutException("Timeout")
        mock_client_class.return_value.__aenter__.return_value = mock_client
        
        with self.assertRaises(RagFlowNetworkError):
            await self.service._make_async_request("GET", "http://test.com")

    @patch('httpx.AsyncClient')
    async def test_make_async_request_connect_error(self, mock_client_class):
        """测试异步请求连接错误"""
        mock_client = AsyncMock()
        mock_client.request.side_effect = httpx.ConnectError("Connection failed")
        mock_client_class.return_value.__aenter__.return_value = mock_client
        
        with self.assertRaises(RagFlowNetworkError):
            await self.service._make_async_request("GET", "http://test.com")

    @patch.object(RagFlowRAGService, '_make_async_request')
    async def test_upload_file_to_rag_service_success(self, mock_request):
        """测试文件上传成功"""
        # 模拟成功响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "code": 0,
            "data": [{"id": "doc-123"}]
        }
        mock_request.return_value = mock_response
        
        result = await self.service.upload_file_to_rag_service(
            file_content=self.test_file_content,
            file_name=self.test_file_name,
            file_size=self.test_file_size,
            file_hash=self.test_file_hash,
            file_type=self.test_file_type
        )
        
        self.assertIsInstance(result, FileUploadResult)
        self.assertTrue(result.success)
        self.assertEqual(result.rag_file_id, "doc-123")
        self.assertEqual(result.status, FileStatus.UPLOADING)

    @patch.object(RagFlowRAGService, '_make_async_request')
    async def test_upload_file_to_rag_service_api_error(self, mock_request):
        """测试文件上传API错误"""
        # 模拟API错误响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "code": 102,
            "message": "Dataset not found"
        }
        mock_request.return_value = mock_response
        
        result = await self.service.upload_file_to_rag_service(
            file_content=self.test_file_content,
            file_name=self.test_file_name,
            file_size=self.test_file_size,
            file_hash=self.test_file_hash,
            file_type=self.test_file_type
        )
        
        self.assertIsInstance(result, FileUploadResult)
        self.assertFalse(result.success)
        self.assertEqual(result.status, FileStatus.ERROR)

    @patch.object(RagFlowRAGService, '_make_async_request')
    async def test_upload_file_to_rag_service_network_error(self, mock_request):
        """测试文件上传网络错误"""
        mock_request.side_effect = RagFlowNetworkError("Network error")
        
        result = await self.service.upload_file_to_rag_service(
            file_content=self.test_file_content,
            file_name=self.test_file_name,
            file_size=self.test_file_size,
            file_hash=self.test_file_hash,
            file_type=self.test_file_type
        )
        
        self.assertIsInstance(result, FileUploadResult)
        self.assertFalse(result.success)
        self.assertEqual(result.status, FileStatus.ERROR)

    @patch.object(RagFlowRAGService, '_make_async_request')
    async def test_parse_file_success(self, mock_request):
        """测试文档解析成功"""
        # 模拟成功响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"code": 0}
        mock_request.return_value = mock_response

        result = await self.service.parse_file(self.test_file_id)
        self.assertTrue(result)

    @patch.object(RagFlowRAGService, '_make_async_request')
    async def test_parse_file_api_error(self, mock_request):
        """测试文档解析API错误"""
        # 模拟API错误响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "code": 102,
            "message": "Document not found"
        }
        mock_request.return_value = mock_response

        result = await self.service.parse_file(self.test_file_id)
        self.assertFalse(result)

    @patch.object(RagFlowRAGService, '_make_async_request')
    async def test_parse_file_network_error(self, mock_request):
        """测试文档解析网络错误"""
        mock_request.side_effect = RagFlowNetworkError("Network error")

        result = await self.service.parse_file(self.test_file_id)
        self.assertFalse(result)

    @patch('httpx.AsyncClient')
    async def test_get_file_status_success(self, mock_client_class):
        """测试获取文件状态成功"""
        # 模拟成功响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "code": 0,
            "data": [
                {
                    "id": self.test_file_id,
                    "status": "1",
                    "chunk_count": 5
                }
            ]
        }

        # 模拟异步客户端
        mock_client = AsyncMock()
        mock_client.get.return_value = mock_response
        mock_client_class.return_value.__aenter__.return_value = mock_client

        result = await self.service.get_file_status(self.test_file_id)

        self.assertEqual(result["status"], FileStatus.READY)
        self.assertEqual(result["chunk_count"], 5)
        self.assertEqual(result["progress"], 1.0)
        self.assertIsNone(result["error_message"])

    @patch('httpx.AsyncClient')
    async def test_get_file_status_not_found(self, mock_client_class):
        """测试获取文件状态 - 文档未找到"""
        # 模拟成功响应但文档不存在
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "code": 0,
            "data": []  # 空列表，文档不存在
        }

        # 模拟异步客户端
        mock_client = AsyncMock()
        mock_client.get.return_value = mock_response
        mock_client_class.return_value.__aenter__.return_value = mock_client

        result = await self.service.get_file_status(self.test_file_id)

        self.assertEqual(result["status"], FileStatus.ERROR)
        self.assertEqual(result["message"], "文档未找到")
        self.assertEqual(result["progress"], 0.0)
        self.assertIsNotNone(result["error_message"])

    @patch('httpx.AsyncClient')
    async def test_delete_file_from_rag_service_success(self, mock_client_class):
        """测试删除文件成功"""
        # 模拟成功响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"code": 0}

        # 模拟异步客户端
        mock_client = AsyncMock()
        mock_client.delete.return_value = mock_response
        mock_client_class.return_value.__aenter__.return_value = mock_client

        result = await self.service.delete_file_from_rag_service(
            self.test_user_id,
            [self.test_file_id]
        )
        self.assertTrue(result)

    async def test_delete_file_from_rag_service_empty_list(self):
        """测试删除空文件列表"""
        result = await self.service.delete_file_from_rag_service(self.test_user_id, [])
        self.assertTrue(result)  # 空列表应该返回True

    @patch('httpx.AsyncClient')
    async def test_delete_file_from_rag_service_api_error(self, mock_client_class):
        """测试删除文件API错误"""
        # 模拟API错误响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "code": 102,
            "message": "Permission denied"
        }

        # 模拟异步客户端
        mock_client = AsyncMock()
        mock_client.delete.return_value = mock_response
        mock_client_class.return_value.__aenter__.return_value = mock_client

        result = await self.service.delete_file_from_rag_service(
            self.test_user_id,
            [self.test_file_id]
        )
        self.assertFalse(result)

    @patch('httpx.AsyncClient')
    async def test_retrieve_from_documents_success(self, mock_client_class):
        """测试文档检索成功"""
        # 模拟成功响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "code": 0,
            "data": [
                {
                    "id": "chunk-1",
                    "content": "This is test content",
                    "similarity": 0.85,
                    "document_id": self.test_file_id,
                    "document_name": "test.txt",
                    "page_number": 1,
                    "position": {"x": 0, "y": 0},
                    "create_time": "2024-01-01T00:00:00Z",
                    "update_time": "2024-01-01T00:00:00Z"
                }
            ]
        }

        # 模拟异步客户端
        mock_client = AsyncMock()
        mock_client.get.return_value = mock_response
        mock_client_class.return_value.__aenter__.return_value = mock_client

        result = await self.service.retrieve_from_documents(
            "test query",
            self.test_user_id,
            [self.test_file_id]
        )

        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]["content"], "This is test content")
        self.assertEqual(result[0]["score"], 0.85)
        self.assertEqual(result[0]["document_id"], self.test_file_id)
        self.assertEqual(result[0]["chunk_id"], "chunk-1")

    async def test_retrieve_from_documents_empty_query(self):
        """测试空查询字符串"""
        result = await self.service.retrieve_from_documents("", self.test_user_id, [self.test_file_id])
        self.assertEqual(result, [])

    async def test_retrieve_from_documents_empty_file_ids(self):
        """测试空文件ID列表"""
        result = await self.service.retrieve_from_documents("test query", self.test_user_id, [])
        self.assertEqual(result, [])

    @patch('httpx.AsyncClient')
    async def test_retrieve_from_documents_api_error(self, mock_client_class):
        """测试文档检索API错误"""
        # 模拟API错误响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "code": 102,
            "message": "Dataset not found"
        }

        # 模拟异步客户端
        mock_client = AsyncMock()
        mock_client.get.return_value = mock_response
        mock_client_class.return_value.__aenter__.return_value = mock_client

        result = await self.service.retrieve_from_documents(
            "test query",
            self.test_user_id,
            [self.test_file_id]
        )
        self.assertEqual(result, [])


if __name__ == '__main__':
    unittest.main()
