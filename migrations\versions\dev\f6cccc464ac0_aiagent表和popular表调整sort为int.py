"""aiagent表和popular表调整sort为int

Revision ID: f6cccc464ac0
Revises: 682b4d77b492
Create Date: 2025-07-29 17:25:13.750881

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

import sqlmodel

# revision identifiers, used by Alembic.
revision: str = 'f6cccc464ac0'
down_revision: Union[str, None] = '682b4d77b492'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('ai_agent', 'sort',
               existing_type=sa.VARCHAR(),
               type_=sa.Integer(),
               existing_nullable=False,
               existing_server_default=sa.text("'0'::character varying"))
    op.alter_column('popular_question', 'sort',
               existing_type=sa.VARCHAR(),
               type_=sa.Integer(),
               existing_nullable=False,
               existing_server_default=sa.text('0'))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('popular_question', 'sort',
               existing_type=sa.Integer(),
               type_=sa.VARCHAR(),
               existing_nullable=False,
               existing_server_default=sa.text('0'))
    op.alter_column('ai_agent', 'sort',
               existing_type=sa.Integer(),
               type_=sa.VARCHAR(),
               existing_nullable=False,
               existing_server_default=sa.text("'0'::character varying"))
    # ### end Alembic commands ###
