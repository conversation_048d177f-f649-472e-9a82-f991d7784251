"""prompt_template del order

Revision ID: 2926ee678fb7
Revises: 61b11fb71aaf
Create Date: 2025-07-22 17:50:44.287704

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

import sqlmodel

# revision identifiers, used by Alembic.
revision: str = '2926ee678fb7'
down_revision: Union[str, None] = '61b11fb71aaf'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('prompt_template', sa.Column('sort', sa.Integer(), nullable=False))
    op.drop_column('prompt_template', 'order')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('prompt_template', sa.Column('order', sa.INTEGER(), autoincrement=False, nullable=False))
    op.drop_column('prompt_template', 'sort')
    # ### end Alembic commands ###
