"""rename_user_uploaded_files_columns

Revision ID: 319bf9434c95
Revises: 260dd70ca243
Create Date: 2025-09-11 09:45:15.580377

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

import sqlmodel

# revision identifiers, used by Alembic.
revision: str = '319bf9434c95'
down_revision: Union[str, None] = '260dd70ca243'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Rename columns to remove 'file_' prefix
    op.alter_column('user_uploaded_files', 'file_name', new_column_name='name')
    op.alter_column('user_uploaded_files', 'file_hash', new_column_name='hash')
    op.alter_column('user_uploaded_files', 'file_size', new_column_name='size')
    op.alter_column('user_uploaded_files', 'file_type', new_column_name='type')

    # Update indexes to use new column names
    op.drop_index('ix_user_uploaded_files_file_hash', table_name='user_uploaded_files')
    op.drop_index('idx_file_hash_user', table_name='user_uploaded_files')
    op.create_index('idx_file_hash_user', 'user_uploaded_files', ['hash', 'user_id'], unique=False)
    op.create_index(op.f('ix_user_uploaded_files_hash'), 'user_uploaded_files', ['hash'], unique=False)

    # Update check constraint to use new column name
    op.drop_constraint('check_positive_file_size', 'user_uploaded_files', type_='check')
    op.create_check_constraint('check_positive_file_size', 'user_uploaded_files', 'size > 0')


def downgrade() -> None:
    """Downgrade schema."""
    # Revert check constraint to use old column name
    op.drop_constraint('check_positive_file_size', 'user_uploaded_files', type_='check')
    op.create_check_constraint('check_positive_file_size', 'user_uploaded_files', 'file_size > 0')

    # Revert indexes to use old column names
    op.drop_index(op.f('ix_user_uploaded_files_hash'), table_name='user_uploaded_files')
    op.drop_index('idx_file_hash_user', table_name='user_uploaded_files')
    op.create_index('idx_file_hash_user', 'user_uploaded_files', ['file_hash', 'user_id'], unique=False)
    op.create_index('ix_user_uploaded_files_file_hash', 'user_uploaded_files', ['file_hash'], unique=False)

    # Rename columns back to original names with 'file_' prefix
    op.alter_column('user_uploaded_files', 'name', new_column_name='file_name')
    op.alter_column('user_uploaded_files', 'hash', new_column_name='file_hash')
    op.alter_column('user_uploaded_files', 'size', new_column_name='file_size')
    op.alter_column('user_uploaded_files', 'type', new_column_name='file_type')
