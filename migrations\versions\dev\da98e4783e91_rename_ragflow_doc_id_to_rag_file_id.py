"""rename_ragflow_doc_id_to_rag_file_id

Revision ID: da98e4783e91
Revises: 319bf9434c95
Create Date: 2025-09-11 10:48:34.177017

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

import sqlmodel

# revision identifiers, used by Alembic.
revision: str = 'da98e4783e91'
down_revision: Union[str, None] = '319bf9434c95'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Rename ragflow_doc_id column to rag_file_id
    op.alter_column('user_uploaded_files', 'ragflow_doc_id', new_column_name='rag_file_id')

    # Update indexes to use new column name
    op.drop_index('ix_user_uploaded_files_ragflow_doc_id', table_name='user_uploaded_files')
    op.drop_index('idx_ragflow_doc', table_name='user_uploaded_files')

    op.create_index('ix_user_uploaded_files_rag_file_id', 'user_uploaded_files', ['rag_file_id'], unique=False)
    op.create_index('idx_ragflow_doc', 'user_uploaded_files', ['rag_file_id'], unique=False)


def downgrade() -> None:
    """Downgrade schema."""
    # Revert indexes to use old column name
    op.drop_index('ix_user_uploaded_files_rag_file_id', table_name='user_uploaded_files')
    op.drop_index('idx_ragflow_doc', table_name='user_uploaded_files')

    op.create_index('ix_user_uploaded_files_ragflow_doc_id', 'user_uploaded_files', ['ragflow_doc_id'], unique=False)
    op.create_index('idx_ragflow_doc', 'user_uploaded_files', ['ragflow_doc_id'], unique=False)

    # Rename rag_file_id column back to ragflow_doc_id
    op.alter_column('user_uploaded_files', 'rag_file_id', new_column_name='ragflow_doc_id')
