import unittest
import async<PERSON>
from datetime import datetime, timezone
from unittest.mock import AsyncMock, MagicMock
from core.services.database.schemas.user_uploaded_files import (
    UserUploadedFilesTable,
    UserUploadedFilesCreate,
    UserUploadedFilesUpdate,
    FileStatus
)
from core.services.database.crud.user_uploaded_files import user_uploaded_files_crud


class TestUserUploadedFiles(unittest.TestCase):
    """测试用户上传文件模型和CRUD操作"""

    def test_file_status_enum(self):
        """测试文件状态枚举"""
        self.assertEqual(FileStatus.UPLOADING, "uploading")
        self.assertEqual(FileStatus.PARSING, "parsing")
        self.assertEqual(FileStatus.READY, "ready")
        self.assertEqual(FileStatus.ERROR, "error")

    def test_user_uploaded_files_create_model(self):
        """测试创建用户上传文件模型"""
        file_data = UserUploadedFilesCreate(
            user_id=123,
            name="test_document.pdf",
            hash="abc123def456",
            size=1024000,
            type="application/pdf",
            rag_file_id="ragflow_doc_123",
            rag_server="ragflow",
            status=FileStatus.UPLOADING
        )

        self.assertEqual(file_data.user_id, 123)
        self.assertEqual(file_data.name, "test_document.pdf")
        self.assertEqual(file_data.hash, "abc123def456")
        self.assertEqual(file_data.size, 1024000)
        self.assertEqual(file_data.type, "application/pdf")
        self.assertEqual(file_data.rag_file_id, "ragflow_doc_123")
        self.assertEqual(file_data.rag_server, "ragflow")
        self.assertEqual(file_data.status, FileStatus.UPLOADING)

    def test_user_uploaded_files_update_model(self):
        """测试更新用户上传文件模型"""
        update_data = UserUploadedFilesUpdate(
            status=FileStatus.READY,
            error_message=None,
            rag_server="langchain"
        )

        self.assertEqual(update_data.status, FileStatus.READY)
        self.assertIsNone(update_data.error_message)
        self.assertEqual(update_data.rag_server, "langchain")

    def test_rag_server_default_value(self):
        """测试rag_server字段的默认值"""
        # 创建不指定rag_server的文件记录，应该使用默认值
        file_data = UserUploadedFilesCreate(
            user_id=456,
            name="test_without_rag_server.txt",
            hash="def456ghi789",
            size=2048,
            type="text/plain",
            rag_file_id="ragflow_doc_456",
            status=FileStatus.PARSING
        )

        # 验证默认值为"ragflow"
        self.assertEqual(file_data.rag_server, "ragflow")

    def test_rag_server_custom_value(self):
        """测试rag_server字段的自定义值"""
        # 测试不同的RAG服务提供商
        test_cases = ["langchain", "llamaindex", "custom_rag"]

        for rag_service in test_cases:
            with self.subTest(rag_service=rag_service):
                file_data = UserUploadedFilesCreate(
                    user_id=789,
                    name=f"test_{rag_service}.pdf",
                    hash=f"hash_{rag_service}",
                    size=4096,
                    type="application/pdf",
                    rag_file_id=f"{rag_service}_doc_789",
                    rag_server=rag_service,
                    status=FileStatus.READY
                )

                self.assertEqual(file_data.rag_server, rag_service)

    def test_user_uploaded_files_table_model(self):
        """测试数据库表模型"""
        # 验证表名
        self.assertEqual(UserUploadedFilesTable.__tablename__, "user_uploaded_files")
        
        # 验证主键字段存在
        self.assertTrue(hasattr(UserUploadedFilesTable, 'id'))
        self.assertTrue(hasattr(UserUploadedFilesTable, 'user_id'))
        self.assertTrue(hasattr(UserUploadedFilesTable, 'name'))
        self.assertTrue(hasattr(UserUploadedFilesTable, 'hash'))
        self.assertTrue(hasattr(UserUploadedFilesTable, 'size'))
        self.assertTrue(hasattr(UserUploadedFilesTable, 'type'))
        self.assertTrue(hasattr(UserUploadedFilesTable, 'rag_file_id'))
        self.assertTrue(hasattr(UserUploadedFilesTable, 'status'))
        self.assertTrue(hasattr(UserUploadedFilesTable, 'error_message'))
        self.assertTrue(hasattr(UserUploadedFilesTable, 'created_at'))
        self.assertTrue(hasattr(UserUploadedFilesTable, 'updated_at'))

    async def test_crud_operations(self):
        """测试CRUD操作（模拟数据库会话）"""
        # 创建模拟的数据库会话
        mock_db = AsyncMock()
        
        # 测试数据
        test_file = UserUploadedFilesCreate(
            user_id=123,
            name="test.pdf",
            hash="hash123",
            size=1024,
            type="application/pdf",
            rag_file_id="ragflow123",
            rag_server="ragflow",
            status=FileStatus.UPLOADING
        )
        
        # 模拟创建操作
        mock_db.add = MagicMock()
        mock_db.commit = AsyncMock()
        mock_db.refresh = AsyncMock()
        
        # 这里只是验证CRUD方法存在并可以调用
        self.assertTrue(hasattr(user_uploaded_files_crud, 'create'))
        self.assertTrue(hasattr(user_uploaded_files_crud, 'get'))
        self.assertTrue(hasattr(user_uploaded_files_crud, 'get_by_user_id'))
        self.assertTrue(hasattr(user_uploaded_files_crud, 'get_by_file_hash'))
        self.assertTrue(hasattr(user_uploaded_files_crud, 'get_by_rag_file_id'))
        self.assertTrue(hasattr(user_uploaded_files_crud, 'get_by_status'))
        self.assertTrue(hasattr(user_uploaded_files_crud, 'update'))
        self.assertTrue(hasattr(user_uploaded_files_crud, 'update_status'))
        self.assertTrue(hasattr(user_uploaded_files_crud, 'delete'))
        self.assertTrue(hasattr(user_uploaded_files_crud, 'count_by_user'))

    def test_file_size_validation(self):
        """测试文件大小验证逻辑"""
        # 正常文件大小
        valid_file = UserUploadedFilesCreate(
            user_id=123,
            name="test.pdf",
            hash="hash123",
            size=1024,  # 正数
            type="application/pdf",
            rag_file_id="ragflow123"
        )
        self.assertGreater(valid_file.size, 0)

    def test_mime_type_examples(self):
        """测试常见MIME类型"""
        mime_types = [
            "application/pdf",
            "text/plain",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "text/csv",
            "application/json"
        ]
        
        for mime_type in mime_types:
            file_data = UserUploadedFilesCreate(
                user_id=123,
                name=f"test.{mime_type.split('/')[-1]}",
                hash="hash123",
                size=1024,
                type=mime_type,
                rag_file_id="ragflow123"
            )
            self.assertEqual(file_data.type, mime_type)
            self.assertLessEqual(len(file_data.type), 50)  # 验证长度限制


if __name__ == '__main__':
    unittest.main()
