"""调整提示词模版字段disable类型为int

Revision ID: d4dbeed2f0b9
Revises: fe6b27e345c7
Create Date: 2025-07-15 19:34:41.308645

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

import sqlmodel

# revision identifiers, used by Alembic.
revision: str = 'd4dbeed2f0b9'
down_revision: Union[str, None] = 'fe6b27e345c7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('prompt_template', 'disabled',
               existing_type=sa.BOOLEAN(),
               type_=sa.Integer(),
               existing_nullable=False,
               existing_server_default=sa.text('false'))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('prompt_template', 'disabled',
               existing_type=sa.Integer(),
               type_=sa.BOOLEAN(),
               existing_nullable=False,
               existing_server_default=sa.text('false'))
    # ### end Alembic commands ###
