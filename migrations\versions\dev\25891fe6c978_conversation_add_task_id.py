"""conversation add task_id

Revision ID: 25891fe6c978
Revises: d4dbeed2f0b9
Create Date: 2025-07-17 10:18:13.744861

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

import sqlmodel

# revision identifiers, used by Alembic.
revision: str = "25891fe6c978"
down_revision: Union[str, None] = "d4dbeed2f0b9"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "ai_agent", "base_url", existing_type=sa.VARCHAR(length=255), nullable=True
    )
    op.alter_column(
        "ai_agent", "api_key", existing_type=sa.VARCHAR(length=255), nullable=True
    )
    op.alter_column(
        "ai_agent", "router_url", existing_type=sa.VARCHAR(length=255), nullable=True
    )
    op.alter_column(
        "ai_agent",
        "disabled",
        existing_type=sa.VARCHAR(),
        nullable=False,
        existing_server_default=sa.text("0"),
    )
    op.alter_column(
        "ai_agent",
        "create_by",
        existing_type=sa.VARCHAR(),
        nullable=False,
        server_default="",
    )
    op.drop_column("ai_agent", "welcome_statement")
    op.drop_column("ai_agent", "sort")
    op.add_column(
        "conversation",
        sa.Column("task_id", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    )
    op.alter_column(
        "popular_question",
        "create_by",
        existing_type=sa.VARCHAR(),
        nullable=False,
        server_default="",
    )
    op.alter_column(
        "prompt_template", "sort", existing_type=sa.VARCHAR(), nullable=False
    )
    op.add_column(
        "recommend_question",
        sa.Column("order", sa.Integer(), nullable=False, server_default="0"),
    )
    op.alter_column(
        "recommend_question",
        "disabled",
        existing_type=sa.VARCHAR(),
        type_=sa.Integer(),
        nullable=False,
        postgresql_using="disabled::integer",
    )
    op.alter_column(
        "recommend_question",
        "create_by",
        existing_type=sa.VARCHAR(),
        nullable=False,
        server_default="",
    )
    op.alter_column(
        "recommend_question", "update_by", existing_type=sa.VARCHAR(), nullable=False
    )
    op.alter_column(
        "recommend_question",
        "id",
        existing_type=sa.BIGINT(),
        type_=sa.Integer(),
        existing_nullable=False,
        autoincrement=True,
    )
    op.drop_column("recommend_question", "sort")
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "recommend_question",
        sa.Column("sort", sa.VARCHAR(), autoincrement=False, nullable=True),
    )
    op.alter_column(
        "recommend_question",
        "id",
        existing_type=sa.Integer(),
        type_=sa.BIGINT(),
        existing_nullable=False,
        autoincrement=True,
    )
    op.alter_column(
        "recommend_question", "update_by", existing_type=sa.VARCHAR(), nullable=True
    )
    op.alter_column(
        "recommend_question",
        "create_by",
        existing_type=sa.VARCHAR(),
        nullable=True,
        server_default="",
    )
    op.alter_column(
        "recommend_question",
        "disabled",
        existing_type=sa.Integer(),
        type_=sa.VARCHAR(),
        nullable=True,
    )
    op.drop_column("recommend_question", "order")
    op.alter_column(
        "prompt_template", "sort", existing_type=sa.VARCHAR(), nullable=True
    )
    op.alter_column(
        "popular_question",
        "create_by",
        existing_type=sa.VARCHAR(),
        nullable=True,
        server_default="",
    )
    op.drop_column("conversation", "task_id")
    op.add_column(
        "ai_agent", sa.Column("sort", sa.VARCHAR(), autoincrement=False, nullable=True)
    )
    op.add_column(
        "ai_agent",
        sa.Column(
            "welcome_statement", sa.VARCHAR(), autoincrement=False, nullable=True
        ),
    )
    op.alter_column(
        "ai_agent",
        "create_by",
        existing_type=sa.VARCHAR(),
        nullable=True,
        server_default="",
    )
    op.alter_column(
        "ai_agent",
        "disabled",
        existing_type=sa.VARCHAR(),
        nullable=True,
        existing_server_default=sa.text("0"),
    )
    op.alter_column(
        "ai_agent", "router_url", existing_type=sa.VARCHAR(length=255), nullable=False
    )
    op.alter_column(
        "ai_agent", "api_key", existing_type=sa.VARCHAR(length=255), nullable=False
    )
    op.alter_column(
        "ai_agent", "base_url", existing_type=sa.VARCHAR(length=255), nullable=False
    )
    # ### end Alembic commands ###
