"""智能体管理表和模型管理表新增模型code字段

Revision ID: 379a8233d7e6
Revises: 75a41661aaf0
Create Date: 2025-08-13 17:01:23.335702

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

import sqlmodel

# revision identifiers, used by Alembic.
revision: str = '379a8233d7e6'
down_revision: Union[str, None] = '75a41661aaf0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('ai_agent', sa.Column('model_code', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    op.add_column('model_management', sa.Column('model_code', sqlmodel.sql.sqltypes.AutoString(), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('model_management', 'model_code')
    op.drop_column('ai_agent', 'model_code')
    # ### end Alembic commands ###
