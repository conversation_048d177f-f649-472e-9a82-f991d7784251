"""update prompt_template add created_by

Revision ID: c2a46b8f66ca
Revises: 23e2cf43a4f7
Create Date: 2025-07-15 09:44:31.857631

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

import sqlmodel

# revision identifiers, used by Alembic.
revision: str = 'c2a46b8f66ca'
down_revision: Union[str, None] = '23e2cf43a4f7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('prompt_template', sa.Column('created_by', sqlmodel.sql.sqltypes.AutoString(), nullable=False, server_default=""))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('prompt_template', 'created_by')
    # ### end Alembic commands ###
