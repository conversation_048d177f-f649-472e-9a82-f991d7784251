"""initial migration for agent-db-prod

Revision ID: 53f318d28421
Revises: 
Create Date: 2025-09-11 18:28:57.819563

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

import sqlmodel

# revision identifiers, used by Alembic.
revision: str = '53f318d28421'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint(None, 'conversation', ['id'])
    op.add_column('message', sa.Column('hidden', sa.<PERSON>(), nullable=False, server_default="false"))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('message', 'hidden')
    op.drop_constraint(None, 'conversation', type_='unique')
    # ### end Alembic commands ###
