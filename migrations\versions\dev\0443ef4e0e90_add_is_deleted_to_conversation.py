"""add_is_deleted_to_conversation

Revision ID: 0443ef4e0e90
Revises: b1354ac9aaf3
Create Date: 2025-07-11 17:44:56.183041

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

import sqlmodel

# revision identifiers, used by Alembic.
revision: str = '0443ef4e0e90'
down_revision: Union[str, None] = 'b1354ac9aaf3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Add is_deleted column to conversation table with default value False
    op.add_column('conversation', sa.Column('is_deleted', sa.<PERSON>(), nullable=False, server_default='false'))


def downgrade() -> None:
    """Downgrade schema."""
    # Remove is_deleted column from conversation table
    op.drop_column('conversation', 'is_deleted')
