"""智能体管理新增welcome_statement字段

Revision ID: f2f24ba32bbf
Revises: 4806ea67937c
Create Date: 2025-07-17 17:37:27.977096

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

import sqlmodel

# revision identifiers, used by Alembic.
revision: str = 'f2f24ba32bbf'
down_revision: Union[str, None] = '4806ea67937c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('ai_agent', sa.Column('welcome_statement', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('ai_agent', 'welcome_statement')
    # ### end Alembic commands ###
