"""aiagent表中新增分类字段

Revision ID: 682b4d77b492
Revises: a9a233fcb953
Create Date: 2025-07-29 16:01:56.013463

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import sqlmodel

# revision identifiers, used by Alembic.
revision: str = '682b4d77b492'
down_revision: Union[str, None] = 'a9a233fcb953'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('checkpoint_migrations')
    op.drop_index('checkpoint_blobs_thread_id_idx', table_name='checkpoint_blobs')
    op.drop_table('checkpoint_blobs')
    op.drop_index('checkpoints_thread_id_idx', table_name='checkpoints')
    op.drop_table('checkpoints')
    op.drop_table('ai_agent2')
    op.drop_index('checkpoint_writes_thread_id_idx', table_name='checkpoint_writes')
    op.drop_table('checkpoint_writes')
    op.add_column('ai_agent', sa.Column('classify', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    op.drop_column('popular_question', 'sort_1')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('popular_question', sa.Column('sort_1', sa.INTEGER(), autoincrement=False, nullable=True))
    op.drop_column('ai_agent', 'classify')
    op.create_table('checkpoint_writes',
    sa.Column('thread_id', sa.TEXT(), autoincrement=False, nullable=False),
    sa.Column('checkpoint_ns', sa.TEXT(), server_default=sa.text("''::text"), autoincrement=False, nullable=False),
    sa.Column('checkpoint_id', sa.TEXT(), autoincrement=False, nullable=False),
    sa.Column('task_id', sa.TEXT(), autoincrement=False, nullable=False),
    sa.Column('idx', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('channel', sa.TEXT(), autoincrement=False, nullable=False),
    sa.Column('type', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('blob', postgresql.BYTEA(), autoincrement=False, nullable=False),
    sa.Column('task_path', sa.TEXT(), server_default=sa.text("''::text"), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('thread_id', 'checkpoint_ns', 'checkpoint_id', 'task_id', 'idx', name='checkpoint_writes_pkey')
    )
    op.create_index('checkpoint_writes_thread_id_idx', 'checkpoint_writes', ['thread_id'], unique=False)
    op.create_table('ai_agent2',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('agent_code', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('status', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='ai_agent2_pkey'),
    sa.UniqueConstraint('agent_code', name='ai_agent2_agent_code_key', postgresql_include=[], postgresql_nulls_not_distinct=False)
    )
    op.create_table('checkpoints',
    sa.Column('thread_id', sa.TEXT(), autoincrement=False, nullable=False),
    sa.Column('checkpoint_ns', sa.TEXT(), server_default=sa.text("''::text"), autoincrement=False, nullable=False),
    sa.Column('checkpoint_id', sa.TEXT(), autoincrement=False, nullable=False),
    sa.Column('parent_checkpoint_id', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('type', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('checkpoint', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=False),
    sa.Column('metadata', postgresql.JSONB(astext_type=sa.Text()), server_default=sa.text("'{}'::jsonb"), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('thread_id', 'checkpoint_ns', 'checkpoint_id', name='checkpoints_pkey')
    )
    op.create_index('checkpoints_thread_id_idx', 'checkpoints', ['thread_id'], unique=False)
    op.create_table('checkpoint_blobs',
    sa.Column('thread_id', sa.TEXT(), autoincrement=False, nullable=False),
    sa.Column('checkpoint_ns', sa.TEXT(), server_default=sa.text("''::text"), autoincrement=False, nullable=False),
    sa.Column('channel', sa.TEXT(), autoincrement=False, nullable=False),
    sa.Column('version', sa.TEXT(), autoincrement=False, nullable=False),
    sa.Column('type', sa.TEXT(), autoincrement=False, nullable=False),
    sa.Column('blob', postgresql.BYTEA(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('thread_id', 'checkpoint_ns', 'channel', 'version', name='checkpoint_blobs_pkey')
    )
    op.create_index('checkpoint_blobs_thread_id_idx', 'checkpoint_blobs', ['thread_id'], unique=False)
    op.create_table('checkpoint_migrations',
    sa.Column('v', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('v', name='checkpoint_migrations_pkey')
    )
    # ### end Alembic commands ###
