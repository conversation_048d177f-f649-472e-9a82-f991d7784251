"""add_popular_question_table

Revision ID: b36e4ebed07e
Revises: f1202d25f189
Create Date: 2025-06-12 09:49:12.264359

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

import sqlmodel

# revision identifiers, used by Alembic.
revision: str = 'b36e4ebed07e'
down_revision: Union[str, None] = 'f1202d25f189'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('popular_question',
    sa.Column('agent_code', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('prompt', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.<PERSON>umn('heat', sa.Integer(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_popular_question_agent_code'), 'popular_question', ['agent_code'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_popular_question_agent_code'), table_name='popular_question')
    op.drop_table('popular_question')
    # ### end Alembic commands ###
