"""prompt_template del sort

Revision ID: 61b11fb71aaf
Revises: f2f24ba32bbf
Create Date: 2025-07-22 17:40:06.020636

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

import sqlmodel

# revision identifiers, used by Alembic.
revision: str = '61b11fb71aaf'
down_revision: Union[str, None] = 'f2f24ba32bbf'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('ai_agent', 'status',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.alter_column('ai_agent', 'id',
               existing_type=sa.BIGINT(),
               type_=sa.Integer(),
               existing_nullable=False,
               autoincrement=True)
    op.drop_column('prompt_template', 'sort')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('prompt_template', sa.Column('sort', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.alter_column('ai_agent', 'id',
               existing_type=sa.Integer(),
               type_=sa.BIGINT(),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('ai_agent', 'status',
               existing_type=sa.INTEGER(),
               nullable=True)
    # ### end Alembic commands ###
