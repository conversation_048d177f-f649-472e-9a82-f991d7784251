"""
RAG (Retrieval-Augmented Generation) API Module

This module provides comprehensive API endpoints for RAG functionality, including:
- Document upload and management
- File processing status tracking
- Semantic document retrieval
- RAG-based response generation

The API integrates with RagFlowRAGService to provide:
- Multi-format document support (PDF, TXT, DOCX, etc.)
- Automatic document parsing and indexing
- Vector-based semantic search
- User-scoped file management with proper access control

All endpoints require authentication and implement proper error handling,
input validation, and comprehensive logging.

Author: AI Assistant
Date: 2025-09-12
"""

from typing import List, Optional, Dict, Any
from fastapi import (
    APIRouter,
    Depends,
    HTTPException,
    UploadFile,
    File,
    Query,
    Path,
    Body,
)
from pydantic import BaseModel, Field
from datetime import datetime

from core.vo.user_vo import UserVO
from core.auth.security import check_user
from core.api.response import StandardResponse, Pagination
from core.services.database import db_manager
from core.services.database.crud.user_uploaded_files import user_uploaded_files_crud
from core.services.database.schemas.user_uploaded_files import (
    UserUploadedFilesRead,
    FileStatus,
)
from core.rag.ragflow import (
    RagFlowRAGService,
    RagFlowError,
    RagFlowAuthenticationError,
    RagFlowNetworkError,
    RagFlowAPIError,
)
from core.config.app_logger import logger

router = APIRouter(prefix="/rag", tags=["RAG"])

# Request/Response Models


class FileUploadResponse(BaseModel):
    """File upload response model"""

    file_id: str = Field(description="Unique file identifier")
    rag_file_id: str = Field(description="RAG service file identifier")
    file_name: str = Field(description="Original file name")
    file_size: int = Field(description="File size in bytes")
    status: FileStatus = Field(description="Current processing status")
    message: str = Field(description="Upload result message")


class FileStatusResponse(BaseModel):
    """File status response model"""

    file_id: str = Field(description="File identifier")
    status: FileStatus = Field(description="Current processing status")
    progress: Optional[float] = Field(None, description="Processing progress (0-1)")
    error_message: Optional[str] = Field(None, description="Error message if failed")
    updated_at: datetime = Field(description="Last update timestamp")


class FileListResponse(BaseModel):
    """File list response model"""

    files: List[UserUploadedFilesRead] = Field(description="List of user files")
    total: int = Field(description="Total number of files")
    page: int = Field(description="Current page number")
    page_size: int = Field(description="Number of files per page")


class DocumentRetrieveRequest(BaseModel):
    """Document retrieval request model"""

    query: str = Field(description="Search query text")
    file_ids: Optional[List[str]] = Field(
        None, description="Specific user uploaded file IDs to search in"
    )
    limit: int = Field(default=10, ge=1, le=50, description="Maximum number of results")
    similarity_threshold: float = Field(
        default=0.1, ge=0.0, le=1.0, description="Minimum similarity score"
    )


class DocumentChunk(BaseModel):
    """Document chunk model"""

    content: str = Field(description="Document chunk content")
    score: float = Field(description="Similarity score")
    document_id: str = Field(description="Source document ID")
    chunk_id: str = Field(description="Chunk identifier")
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Additional metadata"
    )


class DocumentRetrieveResponse(BaseModel):
    """Document retrieval response model"""

    query: str = Field(description="Original search query")
    results: List[DocumentChunk] = Field(description="Retrieved document chunks")
    total_results: int = Field(description="Total number of results found")


class RAGGenerateRequest(BaseModel):
    """RAG generation request model"""

    query: str = Field(description="User query for RAG generation")
    file_ids: Optional[List[str]] = Field(
        None, description="Specific user uploaded file IDs to use for context"
    )
    max_context_length: int = Field(
        default=4000, ge=500, le=8000, description="Maximum context length"
    )
    temperature: float = Field(
        default=0.7, ge=0.0, le=2.0, description="Generation temperature"
    )
    include_sources: bool = Field(
        default=True, description="Whether to include source references"
    )


class RAGGenerateResponse(BaseModel):
    """RAG generation response model"""

    query: str = Field(description="Original user query")
    response: str = Field(description="Generated response")
    sources: Optional[List[DocumentChunk]] = Field(
        None, description="Source documents used"
    )
    context_used: int = Field(description="Number of context tokens used")


class FileBatchDeleteRequest(BaseModel):
    """Batch file deletion request model"""

    file_ids: List[str] = Field(description="List of user uploaded file IDs to delete")


class FileBatchDeleteResponse(BaseModel):
    """Batch file deletion response model"""

    deleted_files: List[str] = Field(description="Successfully deleted file IDs")
    failed_files: List[Dict[str, str]] = Field(
        description="Failed deletions with error messages"
    )
    total_deleted: int = Field(description="Total number of files deleted")
    total_failed: int = Field(description="Total number of failed deletions")


# Helper function to get RAG service instance
def get_rag_service(service_name: str = "ragflow") -> RagFlowRAGService:
    """Get RAG service instance by dynamically loading the configured module

    Args:
        service_name: Name of the RAG service to load (default: "ragflow")

    Returns:
        RagFlowRAGService: Instance of the RAG service

    Raises:
        HTTPException: If service configuration not found or module loading fails
    """
    try:
        # Import config and importlib here to avoid circular imports
        from core.config.app_config import config
        import importlib

        # Get RAG service configuration
        if not hasattr(config, "rag_service") or not config.rag_service:
            logger.error("RAG service configuration not found in application.yml")
            raise HTTPException(
                status_code=500, detail="RAG service configuration not found"
            )

        services = config.rag_service.services
        if not services:
            logger.error("No RAG services configured in application.yml")
            raise HTTPException(status_code=500, detail="No RAG services configured")

        # Find the requested service configuration
        service_config = None
        for service in services:
            if service.get("name") == service_name:
                service_config = service
                break

        if not service_config:
            logger.error(f"RAG service '{service_name}' not found in configuration")
            raise HTTPException(
                status_code=500, detail=f"RAG service '{service_name}' not configured"
            )

        # Get the module path from configuration
        module_path = service_config.get("module_path")
        if not module_path:
            logger.error(f"No module_path specified for RAG service '{service_name}'")
            raise HTTPException(
                status_code=500,
                detail=f"Module path not configured for service '{service_name}'",
            )

        # Dynamically import the module
        try:
            module = importlib.import_module(module_path)
            logger.info(f"Successfully imported RAG service module: {module_path}")
        except ImportError as e:
            logger.error(f"Failed to import RAG service module '{module_path}': {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to import RAG service module: {module_path}",
            )

        # Get the RagFlowRAGService class from the module
        if not hasattr(module, "RagFlowRAGService"):
            logger.error(
                f"Module '{module_path}' does not contain 'RagFlowRAGService' class"
            )
            raise HTTPException(
                status_code=500,
                detail=f"RagFlowRAGService class not found in module: {module_path}",
            )

        rag_service_class = getattr(module, "RagFlowRAGService")

        # Create and return an instance of the RAG service
        service_instance = rag_service_class()
        logger.info(
            f"Successfully created RAG service instance from module: {module_path}"
        )
        return service_instance

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Unexpected error initializing RAG service '{service_name}': {e}")
        raise HTTPException(status_code=500, detail="RAG service initialization failed")


# File Management Endpoints


@router.post("/files/upload", response_model=StandardResponse[FileUploadResponse])
async def upload_file(
    file: UploadFile = File(..., description="File to upload for RAG processing"),
    user: UserVO = Depends(check_user),
):
    """
    Upload a document for RAG processing

    This endpoint accepts file uploads and processes them through the RAG service.
    The file will be uploaded to RagFlow, parsed, and indexed for later retrieval.

    Args:
        file: The uploaded file (supports PDF, TXT, DOCX, etc.)
        user: Current authenticated user

    Returns:
        StandardResponse containing file upload details and processing status

    Raises:
        HTTPException: If file upload fails or file type is not supported
    """
    try:
        # Validate file
        if not file.filename:
            raise HTTPException(status_code=400, detail="File name is required")

        # Check file size (limit to 50MB)
        MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
        file_content = await file.read()
        if len(file_content) > MAX_FILE_SIZE:
            raise HTTPException(status_code=400, detail="File size exceeds 50MB limit")

        # Check file type
        allowed_types = {
            "application/pdf",
            "text/plain",
            "text/markdown",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/msword",
            "text/csv",
        }
        if file.content_type not in allowed_types:
            raise HTTPException(
                status_code=400,
                detail=f"File type {file.content_type} not supported. Allowed types: {', '.join(allowed_types)}",
            )

        # Initialize RAG service
        rag_service = get_rag_service()

        # Upload file to RAG service
        logger.info(f"User {user.userId} uploading file: {file.filename}")
        result = await rag_service.upload_file(
            user_id=str(user.userId), file_content=file_content, file_name=file.filename
        )

        if not result.success:
            raise HTTPException(
                status_code=500, detail="Failed to upload file to RAG service"
            )

        # Trigger parsing asynchronously
        try:
            await rag_service.parse_file(result.rag_file_id)
            logger.info(f"File parsing triggered for: {result.rag_file_id}")
        except Exception as e:
            logger.warning(f"Failed to trigger parsing for {result.rag_file_id}: {e}")

        # Prepare response
        response_data = FileUploadResponse(
            file_id=result.rag_file_id,  # Using rag_file_id as the main identifier
            rag_file_id=result.rag_file_id,
            file_name=file.filename,
            file_size=len(file_content),
            status=result.status,
            message="File uploaded successfully and parsing initiated",
        )

        return StandardResponse(
            data=response_data, code=200, message="File uploaded successfully"
        )

    except RagFlowAuthenticationError:
        logger.error("RAG service authentication failed")
        raise HTTPException(status_code=401, detail="RAG service authentication failed")
    except RagFlowNetworkError:
        logger.error("RAG service network error")
        raise HTTPException(
            status_code=503, detail="RAG service temporarily unavailable"
        )
    except RagFlowAPIError as e:
        logger.error(f"RAG service API error: {e}")
        raise HTTPException(status_code=500, detail=f"RAG service error: {str(e)}")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error during file upload: {e}")
        raise HTTPException(
            status_code=500, detail="Internal server error during file upload"
        )


@router.get("/files", response_model=StandardResponse[FileListResponse])
async def list_user_files(
    skip: int = Query(0, ge=0, description="Number of files to skip"),
    limit: int = Query(20, ge=1, le=100, description="Number of files to return"),
    status: Optional[FileStatus] = Query(None, description="Filter by file status"),
    user: UserVO = Depends(check_user),
):
    """
    List user's uploaded files with pagination and filtering

    Args:
        skip: Number of files to skip for pagination
        limit: Maximum number of files to return
        status: Optional status filter
        user: Current authenticated user

    Returns:
        StandardResponse containing paginated list of user files
    """
    try:
        async with db_manager.session() as session:
            # Get user files with pagination
            files = await user_uploaded_files_crud.get_by_user_id(
                session, user_id=user.userId, skip=skip, limit=limit, status=status
            )

            # Get total count for pagination
            total = await user_uploaded_files_crud.count_by_user_id(
                session, user_id=user.userId, status=status
            )

            response_data = FileListResponse(
                files=files, total=total, page=skip // limit + 1, page_size=limit
            )

            return StandardResponse(
                data=response_data,
                code=200,
                message="Files retrieved successfully",
                pagination=Pagination(
                    page=skip // limit + 1, pageSize=limit, total=total
                ),
            )

    except Exception as e:
        logger.error(f"Error retrieving user files: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve files")


@router.get("/files/{file_id}", response_model=StandardResponse[UserUploadedFilesRead])
async def get_file_details(
    file_id: str = Path(..., description="File identifier"),
    user: UserVO = Depends(check_user),
):
    """
    Get detailed information about a specific file

    Args:
        file_id: The file identifier
        user: Current authenticated user

    Returns:
        StandardResponse containing file details

    Raises:
        HTTPException: If file not found or access denied
    """
    try:
        async with db_manager.session() as session:
            # Get file by rag_file_id and verify ownership
            file_record = await user_uploaded_files_crud.get_by_rag_file_id(
                session, rag_file_id=file_id
            )

            if not file_record:
                raise HTTPException(status_code=404, detail="File not found")

            if file_record.user_id != user.userId:
                raise HTTPException(status_code=403, detail="Access denied")

            return StandardResponse(
                data=file_record,
                code=200,
                message="File details retrieved successfully",
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving file details: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve file details")


@router.get(
    "/files/{file_id}/status", response_model=StandardResponse[FileStatusResponse]
)
async def get_file_status(
    file_id: str = Path(..., description="File identifier"),
    user: UserVO = Depends(check_user),
):
    """
    Check the processing status of a specific file

    Args:
        file_id: The file identifier
        user: Current authenticated user

    Returns:
        StandardResponse containing file processing status

    Raises:
        HTTPException: If file not found or access denied
    """
    try:
        async with db_manager.session() as session:
            file = await user_uploaded_files_crud.get(session, id=file_id)

            if not file:
                raise HTTPException(status_code=404, detail="File not found")

            rag_file_id = file.rag_file_id

            # Verify file ownership
            file_record = await user_uploaded_files_crud.get_by_rag_file_id(
                session, rag_file_id=rag_file_id
            )

            if not file_record:
                raise HTTPException(
                    status_code=404, detail="File not found from RAG Service"
                )

            if file_record.user_id != user.userId:
                raise HTTPException(status_code=403, detail="Access denied")

        # Get real-time status from RAG service
        rag_service = get_rag_service()
        try:
            status_info = await rag_service.get_file_status(file_id)

            response_data = FileStatusResponse(
                file_id=file_id,
                status=status_info.get("status", file_record.status),
                progress=status_info.get("progress"),
                error_message=status_info.get(
                    "error_message", file_record.error_message
                ),
                updated_at=file_record.updated_at,
            )

            return StandardResponse(
                data=response_data,
                code=200,
                message="File status retrieved successfully",
            )

        except RagFlowError as e:
            logger.warning(f"Failed to get real-time status from RAG service: {e}")
            # Fall back to database status
            response_data = FileStatusResponse(
                file_id=file_id,
                status=file_record.status,
                progress=None,
                error_message=file_record.error_message,
                updated_at=file_record.updated_at,
            )

            return StandardResponse(
                data=response_data,
                code=200,
                message="File status retrieved from database (RAG service unavailable)",
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving file status: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve file status")


@router.post("/files/{file_id}/parse", response_model=StandardResponse[Dict[str, str]])
async def trigger_file_parsing(
    file_id: str = Path(..., description="File identifier"),
    user: UserVO = Depends(check_user),
):
    """
    Manually trigger parsing for a specific file

    This endpoint can be used to retry parsing for files that failed
    or to trigger parsing for files that were uploaded but not parsed.

    Args:
        file_id: The file identifier
        user: Current authenticated user

    Returns:
        StandardResponse indicating parsing trigger result

    Raises:
        HTTPException: If file not found, access denied, or parsing fails
    """
    try:
        async with db_manager.session() as session:
            # Verify file ownership
            file_record = await user_uploaded_files_crud.get_by_rag_file_id(
                session, rag_file_id=file_id
            )

            if not file_record:
                raise HTTPException(status_code=404, detail="File not found")

            if file_record.user_id != user.userId:
                raise HTTPException(status_code=403, detail="Access denied")

        # Trigger parsing through RAG service
        rag_service = get_rag_service()
        success = await rag_service.parse_file(file_id)

        if success:
            return StandardResponse(
                data={"file_id": file_id, "status": "parsing_triggered"},
                code=200,
                message="File parsing triggered successfully",
            )
        else:
            raise HTTPException(
                status_code=500, detail="Failed to trigger file parsing"
            )

    except RagFlowAuthenticationError:
        raise HTTPException(status_code=401, detail="RAG service authentication failed")
    except RagFlowNetworkError:
        raise HTTPException(
            status_code=503, detail="RAG service temporarily unavailable"
        )
    except RagFlowAPIError as e:
        raise HTTPException(status_code=500, detail=f"RAG service error: {str(e)}")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error triggering file parsing: {e}")
        raise HTTPException(status_code=500, detail="Failed to trigger file parsing")


@router.delete("/files", response_model=StandardResponse[FileBatchDeleteResponse])
async def delete_files(
    request: FileBatchDeleteRequest = Body(..., description="File deletion request"),
    user: UserVO = Depends(check_user),
):
    """
    Delete multiple files from both RAG service and database

    This endpoint performs batch deletion of files. It will attempt to delete
    files from the RAG service first, then remove records from the database.

    The file_ids parameter now accepts user-facing file IDs from the
    user_uploaded_files table rather than internal RAG file IDs.

    Args:
        request: Batch deletion request containing user uploaded file IDs
        user: Current authenticated user

    Returns:
        StandardResponse containing deletion results
    """
    try:
        deleted_files = []
        failed_files = []

        # Convert user_uploaded_files IDs to RAG file IDs and verify ownership
        rag_file_ids_to_delete = []
        user_to_rag_mapping = {}  # Track mapping for response
        valid_user_file_ids = []

        async with db_manager.session() as session:
            # Verify each user file ID and build mapping
            for user_file_id in request.file_ids:
                file_record = await user_uploaded_files_crud.get(
                    session, id=user_file_id
                )
                if not file_record:
                    failed_files.append({
                        "file_id": user_file_id,
                        "error": "File not found",
                    })
                elif file_record.user_id != user.userId:
                    failed_files.append({
                        "file_id": user_file_id,
                        "error": "Access denied",
                    })
                else:
                    valid_user_file_ids.append(user_file_id)
                    user_to_rag_mapping[user_file_id] = file_record.rag_file_id
                    rag_file_ids_to_delete.append(file_record.rag_file_id)

        # Get RAG service instance
        rag_service = get_rag_service()

        # TODO: 可能部分删除失败，数据库记录依然被删除，待优化
        # Delete from RAG service first using the converted RAG file IDs
        if rag_file_ids_to_delete:
            try:
                rag_success = await rag_service.delete_file_from_rag_service(
                    str(user.userId), rag_file_ids_to_delete
                )
                if not rag_success:
                    logger.warning(
                        "RAG service deletion returned false, but continuing with database cleanup"
                    )
            except RagFlowError as e:
                logger.warning(
                    f"RAG service deletion failed: {e}, continuing with database cleanup"
                )

        # Delete from database using RAG file IDs, but track user file IDs for response
        async with db_manager.session() as session:
            for user_file_id in valid_user_file_ids:
                rag_file_id = user_to_rag_mapping[user_file_id]
                try:
                    success = await user_uploaded_files_crud.delete_by_rag_file_id(
                        session, rag_file_id=rag_file_id
                    )
                    if success:
                        deleted_files.append(
                            user_file_id
                        )  # Return user file ID in response
                    else:
                        failed_files.append({
                            "file_id": user_file_id,
                            "error": "Database deletion failed",
                        })
                except Exception as e:
                    failed_files.append({"file_id": user_file_id, "error": str(e)})

        response_data = FileBatchDeleteResponse(
            deleted_files=deleted_files,
            failed_files=failed_files,
            total_deleted=len(deleted_files),
            total_failed=len(failed_files),
        )

        return StandardResponse(
            data=response_data,
            code=200,
            message=f"Deletion completed: {len(deleted_files)} succeeded, {len(failed_files)} failed",
        )

    except Exception as e:
        logger.error(f"Error during batch file deletion: {e}")
        raise HTTPException(status_code=500, detail="Failed to delete files")


# Document Retrieval Endpoints


@router.post("/retrieve", response_model=StandardResponse[DocumentRetrieveResponse])
async def retrieve_documents(
    request: DocumentRetrieveRequest = Body(
        ..., description="Document retrieval request"
    ),
    user: UserVO = Depends(check_user),
):
    """
    Retrieve relevant documents based on semantic similarity search

    This endpoint searches through the user's uploaded documents to find
    content that is semantically similar to the provided query.

    The file_ids parameter now accepts user-facing file IDs from the
    user_uploaded_files table rather than internal RAG file IDs.

    Args:
        request: Document retrieval request with query and parameters
        user: Current authenticated user

    Returns:
        StandardResponse containing retrieved document chunks

    Raises:
        HTTPException: If retrieval fails or no documents found
    """
    try:
        # If specific file IDs provided, convert user_uploaded_files IDs to RAG file IDs
        rag_file_ids_to_search = []
        if request.file_ids:
            async with db_manager.session() as session:
                # Get RAG file IDs from user_uploaded_files table IDs
                rag_file_ids_to_search = (
                    await user_uploaded_files_crud.get_rag_file_ids_by_user_file_ids(
                        session, user_file_ids=request.file_ids, user_id=user.userId
                    )
                )

                # Check if all requested files were found and belong to the user
                if len(rag_file_ids_to_search) != len(request.file_ids):
                    raise HTTPException(
                        status_code=403, detail="Some files not found or access denied"
                    )
        else:
            # Get all user's files if no specific files requested
            async with db_manager.session() as session:
                user_files = await user_uploaded_files_crud.get_by_user_id(
                    session,
                    user_id=user.userId,
                    status=FileStatus.READY,  # Only search in ready files
                )
                rag_file_ids_to_search = [f.rag_file_id for f in user_files]

        if not rag_file_ids_to_search:
            return StandardResponse(
                data=DocumentRetrieveResponse(
                    query=request.query, results=[], total_results=0
                ),
                code=200,
                message="No documents available for search",
            )

        # Perform retrieval through RAG service
        rag_service = get_rag_service()
        results = await rag_service.retrieve_from_documents(
            query=request.query,
            user_id=str(user.userId),
            file_ids=rag_file_ids_to_search,
        )

        # Convert results to response format
        document_chunks = []
        for result in results[: request.limit]:
            if result.get("similarity", 0) >= request.similarity_threshold:
                chunk = DocumentChunk(
                    content=result.get("content", ""),
                    score=result.get("similarity", 0.0),
                    document_id=result.get("document_id", ""),
                    chunk_id=result.get("id", ""),
                    metadata=result.get("metadata", {}),
                )
                document_chunks.append(chunk)

        response_data = DocumentRetrieveResponse(
            query=request.query,
            results=document_chunks,
            total_results=len(document_chunks),
        )

        return StandardResponse(
            data=response_data,
            code=200,
            message=f"Retrieved {len(document_chunks)} relevant document chunks",
        )

    except RagFlowAuthenticationError:
        raise HTTPException(status_code=401, detail="RAG service authentication failed")
    except RagFlowNetworkError:
        raise HTTPException(
            status_code=503, detail="RAG service temporarily unavailable"
        )
    except RagFlowAPIError as e:
        raise HTTPException(status_code=500, detail=f"RAG service error: {str(e)}")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error during document retrieval: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve documents")


# RAG Generation Endpoints


@router.post("/search", response_model=StandardResponse[DocumentRetrieveResponse])
async def search_documents(
    query: str = Body(..., description="Search query"),
    file_ids: Optional[List[str]] = Body(
        None, description="Specific user uploaded file IDs to search in"
    ),
    limit: int = Body(10, ge=1, le=50, description="Maximum number of results"),
    similarity_threshold: float = Body(
        0.1, ge=0.0, le=1.0, description="Minimum similarity score"
    ),
    user: UserVO = Depends(check_user),
):
    """
    Advanced document search with filtering options

    This is an alternative endpoint to /retrieve with a simpler request format.
    The file_ids parameter accepts user-facing file IDs from the user_uploaded_files table.

    Args:
        query: Search query text
        file_ids: Optional list of specific user uploaded file IDs to search
        limit: Maximum number of results to return
        similarity_threshold: Minimum similarity score for results
        user: Current authenticated user

    Returns:
        StandardResponse containing search results
    """
    # Reuse the retrieve_documents logic with a constructed request
    request = DocumentRetrieveRequest(
        query=query,
        file_ids=file_ids,
        limit=limit,
        similarity_threshold=similarity_threshold,
    )

    return await retrieve_documents(request, user)
