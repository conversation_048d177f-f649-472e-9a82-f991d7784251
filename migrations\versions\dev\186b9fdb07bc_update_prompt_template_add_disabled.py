"""update prompt_template add disabled

Revision ID: 186b9fdb07bc
Revises: 1d4f9f5c3d7a
Create Date: 2025-07-15 15:38:05.320362

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '186b9fdb07bc'
down_revision: Union[str, None] = '1d4f9f5c3d7a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('prompt_template', sa.<PERSON>umn('disabled', sa.<PERSON>(), nullable=False, server_default='false'))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('prompt_template', 'disabled')
    # ### end Alembic commands ###
